<?xml version="1.0"?>
<ruleset xmlns="http://pmd.sourceforge.net/ruleset/2.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="PMD Ruleset for Drupal" xsi:schemaLocation="http://pmd.sourceforge.net/ruleset/2.0.0 http://pmd.sourceforge.net/ruleset_2_0_0.xsd">
  <description>
    A PMD Ruleset for Drupal coding standards.
  </description>

  <!--
    Include each rule explicitly so we know what we have.
    @see https://github.com/phpmd/phpmd/blob/master/src/main/resources/rulesets/
  -->

  <!-- Clean Code -->
  <rule ref="rulesets/cleancode.xml">
    <exclude name="StaticAccess"/>
    <exclude name="ElseExpression"/>
    <exclude name="MissingImport"/>
    <exclude name="IfStatementAssignment"/>
    <exclude name="BooleanArgumentFlag"/>
  </rule>

  <!-- Code Size -->
  <rule ref="rulesets/codesize.xml/CyclomaticComplexity">
      <priority>1</priority>
      <properties>
        <!-- CC     Type of procedure                           Risk -->
        <!-- 5–10   A well structured and stable procedure      Low -->
        <!-- 11–20  A more complex procedure                    Moderate -->
        <!-- 21–50  A complex procedure, alarming               High -->
          <property name="reportLevel" value="15" />
      </properties>
  </rule>
  <rule ref="rulesets/codesize.xml/NPathComplexity">
    <properties>
      <property name="minimum" value="250" />
    </properties>
  </rule>
  <rule ref="rulesets/codesize.xml/ExcessiveMethodLength">
    <properties>
      <property name="minimum" value="130" />
    </properties>
  </rule>
  <rule ref="rulesets/codesize.xml/ExcessiveClassLength"/>
  <rule ref="rulesets/codesize.xml/ExcessiveParameterList">
    <properties>
      <property name="minimum" value="11" />
    </properties>
  </rule>
  <rule ref="rulesets/codesize.xml/ExcessivePublicCount"/>
  <rule ref="rulesets/codesize.xml/TooManyFields"/>
  <rule ref="rulesets/codesize.xml/TooManyMethods">
    <properties>
      <property name="maxmethods" value="50" />
    </properties>
  </rule>
  <rule ref="rulesets/codesize.xml/ExcessiveClassComplexity">
    <properties>
      <property name="maximum" value="120" />
    </properties>
  </rule>

  <!-- Controversial -->
  <rule ref="rulesets/controversial.xml/Superglobals"/>
  <!--
  These checks do not need to be included since PHPCS will check for style.
  <rule ref="rulesets/controversial.xml/CamelCaseClassName"/>
  <rule ref="rulesets/controversial.xml/CamelCasePropertyName"/>
  <rule ref="rulesets/controversial.xml/CamelCaseMethodName"/>
  <rule ref="rulesets/controversial.xml/CamelCaseParameterName"/>
  <rule ref="rulesets/controversial.xml/CamelCaseVariableName"/>
  -->

  <!-- Design -->
  <rule ref="rulesets/design.xml/ExitExpression"/>
  <rule ref="rulesets/design.xml/EvalExpression"/>
  <rule ref="rulesets/design.xml/GotoStatement"/>
  <rule ref="rulesets/design.xml/NumberOfChildren"/>
  <rule ref="rulesets/design.xml/DepthOfInheritance"/>
  <rule ref="rulesets/design.xml/CouplingBetweenObjects">
  <properties>
   <property name="maximum" value="15"/>
  </properties>
  </rule>
  <rule ref="rulesets/design.xml/DevelopmentCodeFragment"/>
  <rule ref="rulesets/design.xml/EmptyCatchBlock"/>
  <rule ref="rulesets/design.xml/CountInLoopExpression"/>

  <!-- Naming -->
  <rule ref="rulesets/naming.xml/ShortVariable">
    <properties>
      <property name="minimum" value="3"/>
      <!-- Allow $id and $op as a variable name. -->
      <property name="exceptions" description="Comma-separated list of exceptions" value="id,op"/>
    </properties>
  </rule>
  <rule ref="rulesets/naming.xml/LongVariable">
    <properties>
      <!-- Bump variable length to a more reasonable number. -->
      <property name="maximum" description="The variable length reporting threshold" value="40"/>
    </properties>
  </rule>
  <rule ref="rulesets/naming.xml/ShortMethodName">
    <properties>
      <property name="minimum" value="3"/>
      <!-- Allow id method name. -->
      <property name="exceptions" description="Comma-separated list of exceptions" value="id"/>
    </properties>
  </rule>
  <rule ref="rulesets/naming.xml/ConstructorWithNameAsEnclosingClass"/>
  <rule ref="rulesets/naming.xml/ConstantNamingConventions"/>
  <!-- <rule ref="rulesets/naming.xml/BooleanGetMethodName"/> -->

  <!-- Unused Code -->
  <rule ref="rulesets/unusedcode.xml/UnusedPrivateField"/>
  <rule ref="rulesets/unusedcode.xml/UnusedLocalVariable"/>
  <rule ref="rulesets/unusedcode.xml/UnusedPrivateMethod"/>
  <!--
  Hooks often have unused parameters, so ignore this warning.
  @todo is there a way to allow unused parameters in hooks but not elsewhere?
  <rule ref="rulesets/unusedcode.xml/UnusedFormalParameter"/>
  -->

</ruleset>
