{"name": "drupal/recommended-project", "description": "Project template for Drupal projects with a relocated document root", "license": "GPL-2.0-or-later", "type": "project", "homepage": "https://www.drupal.org/project/drupal", "support": {"chat": "https://www.drupal.org/node/314178", "docs": "https://www.drupal.org/docs/user_guide/en/index.html"}, "require": {"php": "^8.3", "composer/installers": "^2.3", "cweagans/composer-patches": "~1.0", "drupal/all_entity_preview": "^1.0@alpha", "drupal/anchor_link": "~3.0", "drupal/better_exposed_filters": "^7.0", "drupal/block_field": "^1.0@RC", "drupal/ckeditor_link_styles": "^1.1", "drupal/clientside_validation": "^4.1", "drupal/config_split": "^2.0", "drupal/core-composer-scaffold": "^10.3", "drupal/core-recommended": "^10.3", "drupal/diff": "^2.0@beta", "drupal/easy_breadcrumb": "^2.0", "drupal/editor_advanced_link": "^2.2", "drupal/editoria11y": "^2.2", "drupal/embed": "^1.10", "drupal/events_log_track": "^4.0", "drupal/field_group": "^4.0@alpha", "drupal/focal_point": "^2.1", "drupal/frontend_editing": "^1.8", "drupal/gin": "^4.0", "drupal/gin_login": "^2.1", "drupal/klaro": "^3.0", "drupal/linkit": "^7.0", "drupal/m4032404": "^1.0@alpha", "drupal/mailsystem": "^4.5", "drupal/media_directories": "^2.2@beta", "drupal/media_entity_download": "^2.4", "drupal/media_file_delete": "^1.3", "drupal/memcache": "^2.7", "drupal/menu_admin_per_menu": "^1.6", "drupal/metatag": "^2.1", "drupal/mimemail": "^1.0@alpha", "drupal/paragraphs": "^1.18", "drupal/pathauto": "^1.13", "drupal/plyr": "^1.0@alpha", "drupal/queue_mail": "^1.7", "drupal/queue_ui": "^3.2", "drupal/quick_node_clone": "^1.22", "drupal/rabbitmq": "^3.1", "drupal/redirect": "^1.11", "drupal/s3fs": "^3.7", "drupal/search_api": "^1.38", "drupal/search_api_autocomplete": "^1.10", "drupal/search_api_db_defaults": "^1.38", "drupal/simple_sitemap": "^4.2", "drupal/smtp": "^1.4", "drupal/storybook": "^1.0@beta", "drupal/twig_tweak": "^3.4", "drupal/twigsuggest": "^2.0@RC", "drupal/ultimate_cron": "^2.0@beta", "drupal/webform": "^6.2", "drupal/webform_extra_field": "^2.0@RC", "drupal/yoast_seo": "^2.1", "drush/drush": "^13.3", "npm-asset/jquery-validation": "^1.21", "npm-asset/northernco--ckeditor5-anchor-drupal": "^0.5.0", "oomphinc/composer-installers-extender": "^2.0"}, "require-dev": {"drupal/core-dev": "~10.3.0", "drupal/devel": "^5.3", "ergebnis/composer-normalize": "^2.42", "mglaman/phpstan-drupal": "^1.3", "phpro/grumphp": "^2.10", "phpstan/extension-installer": "^1.4", "phpstan/phpstan": "^1.12", "phpstan/phpstan-deprecation-rules": "^1.2"}, "conflict": {"drupal/drupal": "*"}, "repositories": [{"type": "composer", "url": "https://packages.drupal.org/8"}, {"type": "composer", "url": "https://asset-packagist.org"}], "minimum-stability": "dev", "prefer-stable": true, "config": {"allow-plugins": {"composer/installers": true, "cweagans/composer-patches": true, "dealerdirect/phpcodesniffer-composer-installer": true, "drupal/core-composer-scaffold": true, "ergebnis/composer-normalize": true, "oomphinc/composer-installers-extender": true, "php-http/discovery": true, "phpro/grumphp": true, "phpstan/extension-installer": true, "tbachert/spi": true}, "sort-packages": true}, "extra": {"drupal-core-project-message": {"include-keys": ["homepage", "support"], "post-create-project-cmd-message": ["<bg=blue;fg=white>                                                         </>", "<bg=blue;fg=white>  Congratulations, you’ve installed the Drupal codebase  </>", "<bg=blue;fg=white>  from the drupal/recommended-project template!          </>", "<bg=blue;fg=white>                                                         </>", "", "<bg=yellow;fg=black>Next steps</>:", "  * Install the site: https://www.drupal.org/docs/installing-drupal", "  * Read the user guide: https://www.drupal.org/docs/user_guide/en/index.html", "  * Get support: https://www.drupal.org/support", "  * Get involved with the Drupal community:", "      https://www.drupal.org/getting-involved", "  * Remove the plugin that prints this message:", "      composer remove drupal/core-project-message"]}, "drupal-scaffold": {"locations": {"web-root": "web/"}}, "grumphp": {"config-default-path": "/home/<USER>/app", "project-path": "/home/<USER>/app"}, "installer-types": ["bower-asset", "npm-asset"], "installer-paths": {"web/core": ["type:drupal-core"], "web/modules/contrib/{$name}": ["type:drupal-module"], "web/profiles/contrib/{$name}": ["type:drupal-profile"], "web/themes/contrib/{$name}": ["type:drupal-theme"], "drush/Commands/contrib/{$name}": ["type:drupal-drush"], "web/modules/custom/{$name}": ["type:drupal-custom-module"], "web/profiles/custom/{$name}": ["type:drupal-custom-profile"], "web/themes/custom/{$name}": ["type:drupal-custom-theme"], "web/libraries/ckeditor5-anchor-drupal": ["npm-asset/northernco--ckeditor5-anchor-drupal"], "web/libraries/{$name}": ["type:drupal-library", "type:bower-asset", "type:npm-asset"]}, "patches": {"drupal/gin": {"Fix breadcrumb issue": "patches/gin/handle_php_errors_on_breadcrumbs.patch"}}}}