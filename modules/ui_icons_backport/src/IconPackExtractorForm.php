<?php

declare(strict_types=1);

namespace Drupal\ui_icons_backport;

use <PERSON><PERSON><PERSON>\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\Core\Plugin\PluginFormBase;
use <PERSON><PERSON>al\Core\Plugin\PluginFormInterface;

/**
 * The icon pack extractor form plugin.
 *
 * @internal
 *   This API is experimental.
 */
class IconPackExtractorForm extends PluginFormBase implements PluginFormInterface {

  /**
   * {@inheritdoc}
   */
  public function buildConfigurationForm(array $form, FormStateInterface $form_state): array {
    return $this->plugin->buildConfigurationForm($form, $form_state);
  }

  /**
   * {@inheritdoc}
   */
  public function validateConfigurationForm(array &$form, FormStateInterface $form_state): void {
    $this->plugin->validateConfigurationForm($form, $form_state);
  }

  /**
   * {@inheritdoc}
   */
  public function submitConfigurationForm(array &$form, FormStateInterface $form_state): void {
    $this->plugin->submitConfigurationForm($form, $form_state);
  }

}
