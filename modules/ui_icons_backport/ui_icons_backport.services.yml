services:
  plugin.manager.icon_pack:
    class: Drupal\ui_icons_backport\Plugin\IconPackManager
    calls:
      - [setValidator, []]
    arguments:
      [
        '@module_handler',
        '@theme_handler',
        '@cache.discovery',
        '@plugin.manager.icon_extractor',
        '@Drupal\Core\Theme\Icon\IconCollector',
        '%app.root%',
      ]
  Drupal\ui_icons_backport\Plugin\IconPackManagerInterface: '@plugin.manager.icon_pack'

  plugin.manager.icon_extractor:
    class: Drupal\ui_icons_backport\IconExtractorPluginManager
    parent: default_plugin_manager
    arguments: ['@plugin_form.factory']
  Drupal\ui_icons_backport\IconExtractorInterface: '@plugin.manager.icon_extractor'

  ui_icons_backport.twig_extension:
    class: Drupal\ui_icons_backport\Template\IconsTwigExtension
    arguments: ['@plugin.manager.icon_pack']
    tags:
      - { name: twig.extension }

  Drupal\Core\Theme\Icon\IconFinder:
    autowire: true
    arguments:
      [
        '@file_url_generator',
        '@logger.channel.default',
        '%app.root%',
      ]

  Drupal\Core\Theme\Icon\IconCollector:
    arguments:
      [
        '@plugin.manager.icon_extractor',
        '@cache.default',
        '@lock',
      ]
    tags:
      - { name: needs_destruction }

  # Alias to support core namespace.
  Drupal\ui_icons_backport\IconFinder: '@Drupal\Core\Theme\Icon\IconFinder'
  Drupal\ui_icons_backport\IconCollector: '@Drupal\Core\Theme\Icon\IconCollector'
