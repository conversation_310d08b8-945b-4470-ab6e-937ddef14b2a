ui_icons_ckeditor5.preview:
  path: '/ui-icons/icon/preview'
  defaults:
    _controller: '\Drupal\ui_icons_ckeditor5\Controller\IconFilterController::preview'
  requirements:
    _permission: 'access content'

ui_icons_ckeditor5.icon_dialog:
  path: '/editor/dialog/icon/{filter_format}'
  defaults:
    _title: 'Icon Dialog'
    _form: 'Drupal\ui_icons_ckeditor5\Form\IconDialog'
  requirements:
    _permission: 'access content'
