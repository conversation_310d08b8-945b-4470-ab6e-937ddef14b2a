<?php

declare(strict_types=1);

namespace Drupal\ui_icons_iconify_api\Plugin\IconExtractor;

use <PERSON><PERSON><PERSON>\Core\Plugin\ContainerFactoryPluginInterface;
use <PERSON><PERSON>al\Core\StringTranslation\TranslatableMarkup;
use <PERSON><PERSON>al\Core\Theme\Icon\Attribute\IconExtractor;
use Drupal\Core\Theme\Icon\Exception\IconPackConfigErrorException;
use Drupal\Core\Theme\Icon\IconDefinition;
use Drupal\Core\Theme\Icon\IconExtractorBase;
use Drupal\Core\Theme\Icon\IconPackExtractorForm;
use Drupal\ui_icons_iconify_api\IconifyApi;
use Drupal\ui_icons_iconify_api\IconifyApiInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Plugin implementation of the icon_extractor.
 */
#[IconExtractor(
  id: 'iconify',
  label: new TranslatableMarkup('Iconify'),
  description: new TranslatableMarkup('Provide Iconify list of Icons.'),
  forms: [
    'settings' => IconPackExtractorForm::class,
  ]
)]
class IconifyExtractor extends IconExtractorBase implements ContainerFactoryPluginInterface {

  /**
   * Constructs a IconifyExtractor object.
   *
   * @param array $configuration
   *   A configuration array containing information about the plugin instance.
   * @param string $plugin_id
   *   The plugin_id for the plugin instance.
   * @param mixed $plugin_definition
   *   The plugin implementation definition.
   * @param \Drupal\ui_icons_iconify_api\IconifyApiInterface $iconifyApi
   *   The Iconify API service.
   */
  public function __construct(
    array $configuration,
    $plugin_id,
    $plugin_definition,
    protected IconifyApiInterface $iconifyApi,
  ) {
    parent::__construct($configuration, $plugin_id, $plugin_definition);
  }

  /**
   * {@inheritdoc}
   */
  public static function create(
    ContainerInterface $container,
    array $configuration,
    $plugin_id,
    $plugin_definition,
  ): self {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->get('ui_icons_iconify.iconify_api')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function discoverIcons(): array {
    $config = $this->configuration['config'] ?? [];

    if (!isset($config['collections'])) {
      throw new IconPackConfigErrorException(sprintf('Missing `config: collections` in your definition, extractor %s require this value.', $this->getPluginId()));
    }

    unset($this->configuration['config']);

    $icons = [];
    foreach ($config['collections'] as $collection) {
      $icons_collection = $this->iconifyApi->getIconsByCollection($collection);
      if (empty($icons_collection)) {
        continue;
      }

      foreach ($icons_collection as $icon_id) {
        if (!is_string($icon_id)) {
          continue;
        }

        $source = sprintf('%s/%s/%s.svg', IconifyApi::API_ENDPOINT, $collection, $icon_id);
        $id = IconDefinition::createIconId($this->configuration['id'], $icon_id);
        $icons[$id] = ['source' => $source];
      }
    }

    return $icons;
  }

}
