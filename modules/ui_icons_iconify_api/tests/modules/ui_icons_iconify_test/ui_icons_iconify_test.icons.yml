test_iconify:
  enabled: false
  extractor: iconify
  config:
    collections:
      - geo
      - gis
  settings:
    size:
      title: "Size"
      type: "integer"
      default: 32
    flip:
      title: "Flip"
      type: "string"
      enum:
        - original
        - horizontal
        - vertical
        - horizontal,vertical
    rotate:
      title: "Rotate"
      type: "string"
      enum:
        - 0deg
        - 90deg
        - 180deg
        - 270deg
    color:
      title: "Color"
      type: "string"
      format: "color"
  template: >-
    {% set params = {
      width: size,
      height: size,
      rotate: rotate,
      flip: flip,
      color: color
    }|filter(v => v is not null) %}
    <img src="{{ source }}?{{ params|url_encode }}" />
