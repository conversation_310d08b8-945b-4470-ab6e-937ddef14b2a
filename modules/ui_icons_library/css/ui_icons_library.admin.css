.card-page {
  display: flex;
}

.sidebar {
  width: 20%;
  margin: 0 1rem 0 1rem;
}

.ui-icons-library-search .sidebar .form-submit {
  margin: 0 1rem 0 0;
}

.card-page .content {
  width: 95%;
}

@media (max-width: 768px) {
  .card-page .sidebar {
    width: 100%;
  }
}

.ui-icons-library-search .filters > div {
  display: inline-block;
}

.ui-icons-library-search .form-submit {
  margin-left: 1rem;
}

.ui-icons-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(32px, 120px));
  grid-gap: 6px;
  margin-block: 6px;
}

.card_grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
}

.card_grid .card {
  padding: 0 1rem 0 1rem;
}

.card.disabled {
  background-color: rgba(255, 72, 0, 0.507);
}

.card .link {
  text-decoration: none;
  color: inherit;
}

.card .link:hover {
  text-decoration: none;
  color: inherit;
}

.card .content svg,
.card .content img,
.card .content i,
.card .content span {
  display: inline-block;
  max-height: 50px;
  margin-right: 0.3rem;
  margin-bottom: 0.3rem;
  padding: 0.3rem;
  border-radius: 0.3rem;
  background-color: rgba(245, 245, 245, 0.507);
}

.card .title {
  margin-bottom: 0;
}

.card .description,
.card .license {
  margin: 0;
}

.card .content {
  padding-top: 1rem;
}
