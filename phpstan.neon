includes:
  - .phpstan-baseline.neon
  - phar://phpstan.phar/conf/bleedingEdge.neon

parameters:
  level: 7

  excludePaths:
    analyseAndScan:
      - */node_modules/*
      - */vendor/*
      - */tests/*
      # Always fail because of missing link_attributes module.
      - */ui_icons_field_link_attributes/src/Plugin/Field/FieldWidget/IconLinkWithAttributesWidget.php
      # Always fail because of missing ui_patterns module.
      - */ui_icons_patterns/src/Plugin/UiPatterns/*
      - */ui_icons_patterns/src/Plugin/Derivative/FieldIconSourceDeriver.php
  fileExtensions:
    - php
    - module
    - inc
    - install
    - theme
    - profile

  treatPhpDocTypesAsCertain: false
  inferPrivatePropertyTypeFromConstructor: true
  reportUnmatchedIgnoredErrors: false

  ignoreErrors:
    - identifier: missingType.generics
    - identifier: missingType.iterableValue
    # Specific ui_icons errors to ignore
    - "#^Call to an undefined method Drupal\\\\Component\\\\Plugin\\\\PluginInspectionInterface#"
    # new static() is a best practice in Drupal, so we cannot fix that.
    - "#^Unsafe usage of new static#"
    # Ignore common errors for now.
    - "#Drupal calls should be avoided in classes, use dependency injection instead#"
    - "#^Plugin definitions cannot be altered.#"
    - "#^Class .* extends @internal class#"
