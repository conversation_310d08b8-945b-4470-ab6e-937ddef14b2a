<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         bootstrap="web/core/tests/bootstrap.php"
         colors="true"
         beStrictAboutTestsThatDoNotTestAnything="true"
         beStrictAboutOutputDuringTests="true"
         beStrictAboutChangesToGlobalState="false"
         failOnWarning="false"
         displayDetailsOnTestsThatTriggerErrors="true"
         displayDetailsOnTestsThatTriggerWarnings="true"
         displayDetailsOnTestsThatTriggerDeprecations="true"
         cacheResult="true"
         xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/10.5/phpunit.xsd"
         cacheDirectory=".phpunit.cache">
  <php>
    <ini name="error_reporting" value="32767"/>
    <ini name="memory_limit" value="-1"/>
    <!-- <env name="SIMPLETEST_BASE_URL" value=""/> -->
    <!-- <env name="SIMPLETEST_DB" value="sqlite://127.0.0.1/:memory:"/> -->

    <env name="SYMFONY_DEPRECATIONS_HELPER" value="disabled"/>
  </php>
  <extensions>
    <!-- Functional tests HTML output logging. -->
    <bootstrap class="Drupal\TestTools\Extension\HtmlLogging\HtmlOutputLogger">
      <!-- The directory where the browser output will be stored. If a relative
        path is specified, it will be relative to the current working directory
        of the process running the PHPUnit CLI. In CI environments, this can be
        overridden by the value set for the "BROWSERTEST_OUTPUT_DIRECTORY"
        environment variable.
      -->
      <parameter name="outputDirectory" value="sites/simpletest/browser_output"/>
      <!-- By default browser tests print the individual links in the test run
        report. To avoid overcrowding the output in CI environments, you can
        set the "verbose" parameter or the "BROWSERTEST_OUTPUT_VERBOSE"
        environment variable to "false". In GitLabCI, the output is saved
        anyway as an artifact that can be browsed or downloaded from Gitlab.
      -->
      <!-- <parameter name="verbose" value="true"/> -->
      <parameter name="verbose" value="false"/>
    </bootstrap>
  </extensions>
  <testsuites>
    <testsuite name="unit">
      <directory>tests/src/Unit</directory>
      <directory>modules/*/tests/src/Unit</directory>
    </testsuite>
    <testsuite name="kernel">
      <directory>tests/src/Kernel</directory>
      <directory>modules/*/tests/src/Kernel</directory>
    </testsuite>
    <testsuite name="functional">
      <!-- <directory>tests/src/Functional</directory> -->
      <directory>modules/*/tests/src/Functional</directory>
    </testsuite>
    <testsuite name="functional-javascript">
      <!-- <directory>tests/src/FunctionalJavascript</directory> -->
      <directory>modules/*/tests/src/FunctionalJavascript</directory>
    </testsuite>
  </testsuites>
  <!-- Settings for coverage reports. -->
  <source ignoreSuppressionOfDeprecations="false">
    <include>
      <directory>./</directory>
    </include>
    <exclude>
      <directory>*/src/Tests</directory>
      <directory>tests</directory>
      <directory>*/tests</directory>
      <directory>modules/*/src/Tests</directory>
      <directory>modules/tests</directory>
      <directory>modules/*/tests</directory>
    </exclude>
  </source>
</phpunit>
